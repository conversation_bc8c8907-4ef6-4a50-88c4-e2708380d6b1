#!/usr/bin/env python3

# Copyright (c) 2024 Navigation2 Contributors
# Licensed under the Apache License, Version 2.0

"""
导航系统监控节点

一次性检查Navigation2系统的关键组件状态，检查所有必需的导航节点的生命周期状态。
对于已经是active状态的节点，会缓存状态避免重复检查。
"""

import rclpy
from rclpy.node import Node
import subprocess


class NavMonitor(Node):
    def __init__(self):
        super().__init__('nav_monitor')

        # 参数 - 与启动文件中的lifecycle_nodes顺序保持一致
        self.declare_parameter('required_nodes', [
            'controller_server',
            'smoother_server',
            'planner_server',
            'behavior_server',
            'velocity_smoother',
            'collision_monitor',
            'bt_navigator',
            'waypoint_follower'
        ])
        self.declare_parameter('service_timeout', 2.0)

        self.required_nodes = self.get_parameter('required_nodes').value
        self.service_timeout = self.get_parameter('service_timeout').value

        # 状态标志
        self.all_nav_ready = False
        self.exit_when_ready = True  # 导航就绪时自动退出

        # 节点状态缓存 - 记录已经检查为active的节点
        self.active_nodes = set()

        self.get_logger().info(f'Navigation monitor started.')
        self.get_logger().info(f'Required nodes: {len(self.required_nodes)}')

        # 执行一次性检查
        self.check_navigation_status()

    def check_nav2_nodes_lifecycle(self):
        """检查Navigation2节点的生命周期状态"""
        self.get_logger().info('检查Navigation2节点生命周期状态:')
        all_active = True

        for node in self.required_nodes:
            # 如果节点已经是active状态，跳过检查
            if node in self.active_nodes:
                self.get_logger().info(f'  ✅ {node}: active (cached)')
                continue

            try:
                # 检查lifecycle状态
                result = subprocess.run(
                    ['ros2', 'lifecycle', 'get', f'/{node}'],
                    capture_output=True, text=True, timeout=10
                )

                if result.returncode == 0:
                    status_output = result.stdout.strip()
                    if "active [3]" in status_output:
                        self.get_logger().info(f'  ✅ {node}: active')
                        # 将节点添加到active缓存中
                        self.active_nodes.add(node)
                    elif "inactive" in status_output:
                        self.get_logger().warning(f'  ❌ {node}: inactive')
                        all_active = False
                    elif "unconfigured" in status_output:
                        self.get_logger().warning(f'  ⚠️ {node}: unconfigured')
                        all_active = False
                    else:
                        self.get_logger().info(f'  ❓ {node}: {status_output}')
                        all_active = False
                else:
                    self.get_logger().warning(f'  ❌ {node}: lifecycle service not available')
                    all_active = False

            except Exception as e:
                self.get_logger().error(f'  ❌ {node}: 错误 - {str(e)}')
                all_active = False

        return all_active

    def check_navigation_status(self):
        """一次性检查导航系统状态"""
        # 检查节点状态
        all_ready = self.check_nav2_nodes_lifecycle()

        if all_ready:
            # 所有组件就绪
            self.all_nav_ready = True
            self.get_logger().info('✅ ALL NAVIGATION COMPONENTS ARE READY!')

            # 如果设置为导航就绪时退出，则退出节点
            if self.exit_when_ready:
                self.exit_node()
        else:
            # 有组件不就绪
            self.all_nav_ready = False
            self.get_logger().warning('❌ Navigation components are not ready')


    def exit_node(self):
        """退出节点以触发OnExecutionComplete事件"""
        self.get_logger().info('✅ Navigation monitor task completed. Exiting...')
        # 触发节点退出
        import sys
        sys.exit(0)


def main(args=None):
    rclpy.init(args=args)

    nav_monitor = NavMonitor()

    try:
        rclpy.spin(nav_monitor)
    except KeyboardInterrupt:
        pass
    finally:
        nav_monitor.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
